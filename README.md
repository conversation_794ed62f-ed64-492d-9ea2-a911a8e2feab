# 💳 CCN Checker

**CCN Checker** is a simple Python-based tool designed to validate Credit Card Numbers (CCNs) quickly and efficiently. It provides a clean terminal output and categorizes the results for easy analysis.

![Tool Preview](https://raw.githubusercontent.com/KianSantang777/CCN-Checker/refs/heads/main/index.png)

---

## 🔧 Features

- ✅ Basic credit card format validation
- 📄 Support for file-based or direct input
- 📊 Categorized results: LIVE / DIE
- ⚡ Lightweight and fast performance

---

## 📦 Installation

```bash
# Clone the repository
git clone https://github.com/KianSantang777/CCN-Checker
cd CCN-Checker

# (Optional) Install required dependencies
pip install -r requirements.txt
```

---

## ▶️ Usage

```bash
python loader.py
```

---

## 📝 Notes

- Make sure Python 3.x is installed on your system.
- Input CCNs should be formatted as required by the script.
- Results will be displayed directly in the terminal.

---

## 🔗 Useful Links

- 🔗 Repository: [github.com/KianSantang777/CCN-Checker](https://github.com/KianSantang777/CCN-Checker)
- 🖼️ Image Preview: ![index.png](https://raw.githubusercontent.com/KianSantang777/CCN-Checker/refs/heads/main/index.png)
